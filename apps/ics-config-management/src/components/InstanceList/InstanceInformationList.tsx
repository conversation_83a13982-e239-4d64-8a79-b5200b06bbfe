import React from 'react';
import {
  Grid,
  Typography,
  Divider,
  Box,
  CircularProgress,
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import DescriptionIcon from '@mui/icons-material/Description';
import DevicesIcon from '@mui/icons-material/Devices';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Instance } from '../../constants/types';
import { AssetIcon, InProgressIcon, MatchedIcon } from '../Icons';

interface InstanceInformationListProps {
  instance: Partial<Instance>;
  statsToNumbers;
  isFetchingConfigInstanceItemStats: Boolean;
  instanceHasAnyAssignments: Boolean;
}

const GridItemStyle = {
  '&.MuiGrid-item': {
    paddingLeft: 0,
    paddingTop: 0,
    padding: '10px',
  },
};

const LoadingSkeleton = () => (
  <Grid item sx={GridItemStyle}>
    <Box display='flex' alignItems='center'>
      <CircularProgress size={20} />
      <Typography
        variant='body2'
        ml={1}
        sx={{
          color: 'black',
        }}
      >
        Loading information...
      </Typography>
    </Box>
  </Grid>
);

const InstanceInformationList = ({
  instance,
  statsToNumbers,
  instanceHasAnyAssignments,
  isFetchingConfigInstanceItemStats,
}: InstanceInformationListProps) => {
  const renderAssignmentInfo = () => {
    if (isFetchingConfigInstanceItemStats) {
      return <LoadingSkeleton />;
    }

    if (instanceHasAnyAssignments) {
      return (
        <>
          {' '}
          <Box display='flex' alignItems='center'>
            <Grid item sx={GridItemStyle}>
              <Box display='flex' alignItems='center'>
                <AssetIcon sx={{ fontSize: 'large' }} />
                <Typography
                  variant='body2'
                  ml={1}
                  sx={{
                    color: 'black',
                  }}
                >
                  {`${statsToNumbers?.deviceCount} Applicable`}
                </Typography>
              </Box>
            </Grid>
            <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
          </Box>
          <Box display='flex' alignItems='center'>
            <Grid item sx={GridItemStyle}>
              <Box display='flex' alignItems='center'>
                <ErrorOutlineIcon color='warning' sx={{ fontSize: 'large' }} />
                <Typography
                  variant='body2'
                  ml={1}
                  sx={{
                    color: 'black',
                  }}
                >
                  {`${statsToNumbers?.notMatchingDeviceCount} Mismatch`}
                </Typography>
              </Box>
            </Grid>
            <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
          </Box>
          <Box display='flex' alignItems='center'>
            <Grid item sx={GridItemStyle}>
              <Box display='flex' alignItems='center'>
                <InProgressIcon sx={{ fontSize: 'large' }} />
                <Typography
                  variant='body2'
                  ml={1}
                  sx={{
                    color: 'black',
                  }}
                >
                  {`${statsToNumbers?.deployingDeviceCount} Active Asset`}
                </Typography>
              </Box>
            </Grid>
            <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
          </Box>
          <Box display='flex' alignItems='center'>
            <Grid item sx={GridItemStyle}>
              <Box display='flex' alignItems='center'>
                <MatchedIcon sx={{ fontSize: 'large' }} />
                <Typography
                  variant='body2'
                  ml={1}
                  sx={{
                    color: 'black',
                  }}
                >
                  {`${statsToNumbers?.matchingDeviceCount} Matching`}
                </Typography>
              </Box>
            </Grid>
          </Box>
        </>
      );
    }

    return (
      <Grid item sx={GridItemStyle}>
        <Box display='flex' alignItems='center'>
          <InfoOutlinedIcon sx={{ color: '#009ACE', fontSize: 'large' }} />
          <Typography
            variant='body2'
            ml={1}
            sx={{
              color: 'black',
            }}
          >
            No Information available
          </Typography>
        </Box>
      </Grid>
    );
  };

  return (
    <Grid
      container
      alignItems='center'
      justifyContent='flex-start'
      paddingTop='20px'
      spacing={2}
    >
      <Box
        display='flex'
        alignItems='center'
        flexGrow={1}
        justifyContent='space-around'
      >
        <Grid item sx={GridItemStyle}>
          <Box display='flex' alignItems='center'>
            <CalendarTodayIcon
              sx={{
                fontSize: 'large',
              }}
            />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`Updated on ${instance?.stats?.lastUpdated}`}
            </Typography>
          </Box>
        </Grid>
        <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
      </Box>

      <Box
        display='flex'
        alignItems='center'
        flexGrow={1}
        justifyContent='space-around'
      >
        <Grid item sx={GridItemStyle}>
          <Box display='flex' alignItems='center'>
            <LocationOnIcon
              sx={{
                fontSize: 'large',
              }}
            />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`${statsToNumbers?.siteCount || 0} Sites`}
            </Typography>
          </Box>
        </Grid>
        <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
      </Box>

      <Box
        display='flex'
        alignItems='center'
        flexGrow={1}
        justifyContent='space-around'
      >
        <Grid item sx={GridItemStyle}>
          <Box display='flex' alignItems='center'>
            <DescriptionIcon
              sx={{
                fontSize: 'large',
              }}
            />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`${instance.totalRevision} Revisions`}
            </Typography>
          </Box>
        </Grid>
        <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
      </Box>

      <Box
        display='flex'
        alignItems='center'
        flexGrow={1}
        justifyContent='space-around'
      >
        <Grid item sx={GridItemStyle}>
          <Box display='flex' alignItems='center'>
            <DevicesIcon
              sx={{
                fontSize: 'large',
              }}
            />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {/* {`${instance.stats.devices} Devices`} */}
              {`${statsToNumbers?.deviceCount} Devices`}
            </Typography>
          </Box>
        </Grid>
        <Divider orientation='vertical' flexItem sx={{ mx: 2 }} />
      </Box>
      {renderAssignmentInfo()}
    </Grid>
  );
};
export { InstanceInformationList };
