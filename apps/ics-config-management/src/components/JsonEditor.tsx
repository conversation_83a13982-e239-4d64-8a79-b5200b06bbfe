import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import Box from '@mui/material/Box';
import isEqual from 'lodash/isEqual';
import Ajv from 'ajv';
import type { RJSFSchema } from '@rjsf/utils';
import ace from 'brace';
import useDebounceCallback from '../hooks/useDebounceCallback';
import {
  getCustomAttributesFromFormData,
  safeJsonParse,
} from './DynamicEditor/utils';
import { generateCustomAttributesAndMutateSchema } from './DynamicEditor/FormSchemaViewer/utils';
import type { UnknownObject } from './DynamicEditor/types';

// Import these after other imports to avoid initialization issues
import 'brace/mode/json';
import 'brace/theme/github';
import 'brace/ext/language_tools';
import 'jsoneditor-react/es/editor.min.css';
import '../styles/jsoneditor.css';

type JsonEditorProps = {
  value?: UnknownObject;
  onChange?: (value: UnknownObject) => void;
  schema?: RJSFSchema;
  originalSchema?: RJSFSchema;
  initialView?: string;
};

export type JsonEditorRef = {
  validate: () => {
    isValid: boolean;
    errors: string[];
  };
};

const ajv = new Ajv({
  allErrors: true,
  removeAdditional: true,
  validateFormats: false,
  verbose: true,
});

const JsonEditor = forwardRef<JsonEditorRef, JsonEditorProps>((props, ref) => {
  const { value, onChange, schema, originalSchema, initialView } = props;
  const editorRef = useRef(null);
  const [currentSchema, setCurrentSchema] = useState(null);
  const [errors, setErrors] = useState({
    parseError: '',
    schemaError: [],
  });
  const [JsonEditorComponent, setJsonEditorComponent] = useState<any>(null);

  /**
   * This function is used to update the schema
   * for Custom Attributes when the user
   * is editing the JSON
   */

  // Dynamically import JsonEditor to avoid initialization issues
  useEffect(() => {
    const loadJsonEditor = async () => {
      try {
        const { JsonEditor: EditorComponent } = await import(
          'jsoneditor-react'
        );
        setJsonEditorComponent(() => EditorComponent);
      } catch (error) {
        console.error('Failed to load JsonEditor:', error);
      }
    };
    loadJsonEditor();
  }, []);

  useEffect(() => {
    if (schema) {
      setCurrentSchema(schema);
    }
  }, [schema]);

  const handleOnChangeText = useDebounceCallback(async newValue => {
    const currentError = {
      parseError: '',
      schemaError: [],
    };
    const parsedValue = safeJsonParse(newValue);
    currentError.parseError = parsedValue.error;
    if (parsedValue.data && initialView === 'form') {
      const extractedCustomAttributes = getCustomAttributesFromFormData({
        formData: parsedValue.data as UnknownObject,
      });
      const { resultSchema } = generateCustomAttributesAndMutateSchema({
        customAttributesObject: extractedCustomAttributes,
        schemaObject: originalSchema,
      });
      if (!isEqual(resultSchema, currentSchema)) {
        setCurrentSchema(resultSchema);
        await editorRef.current?.jsonEditor?.setSchema(resultSchema);
        await editorRef.current?.jsonEditor?.refresh();
      }
      const hasError = await editorRef.current?.jsonEditor?.validate();
      currentError.schemaError =
        hasError?.map(
          ({ instancePath, message }) => `${instancePath} ${message}`
        ) ?? [];
    }
    setErrors(currentError);
  });

  useImperativeHandle(ref, () => ({
    validate: () => {
      const { parseError, schemaError } = errors;
      return {
        isValid: !(schemaError.length || parseError),
        errors: [...schemaError, parseError].filter(Boolean),
      };
    },
  }));

  return (
    <Box
      display='flex'
      flexDirection='column'
      flex='1'
      bgcolor='common.backgroundLight'
      height='100%'
      overflow='auto'
      p={2}
      width='100%'
    >
      <Box
        display='flex'
        flexDirection='column'
        flex='1'
        sx={{
          '& > div': {
            flex: '1',
          },
          '& .jsoneditor': {
            borderColor: 'rgb(225, 225, 236)',
            borderRadius: '16px',
            fontFamily:
              'Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol"',
            overflow: 'hidden',
            '& textarea': {
              fontFamily: 'Menlo, Monaco, Consolas, "Courier New", monospace',
              fontSize: '16px',
              lineHeight: '23px',
              padding: '16px',
            },
            '& .jsoneditor-statusbar': {
              padding: '0px 16px',
            },
            '& .jsoneditor-menu': {
              backgroundColor: 'rgb(182, 182, 191)',
              borderBottomColor: 'rgb(182, 182, 191)',
              padding: '2px 16px',
              '& .jsoneditor-format, & .jsoneditor-compact, & .jsoneditor-sort, & .jsoneditor-transform, & .jsoneditor-poweredBy':
                {
                  display: 'none',
                },
            },
            '& .ace-jsoneditor.ace_editor': {
              fontFamily: 'Menlo, Monaco, Consolas, "Courier New", monospace',
              fontSize: '16px !important',
            },
          },
        }}
      >
        {currentSchema && JsonEditorComponent && (
          <JsonEditorComponent
            ace={ace}
            ajv={ajv}
            allowSchemaSuggestions
            mode={JsonEditorComponent.modes.code}
            onChangeText={handleOnChangeText}
            onChange={onChange}
            ref={editorRef}
            schema={currentSchema}
            value={value}
          />
        )}
      </Box>
    </Box>
  );
});

export default JsonEditor;
