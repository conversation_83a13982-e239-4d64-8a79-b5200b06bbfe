import React from 'react';
import {
  QueryClientProvider,
  QueryClient,
  QueryCache,
} from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { BrowserRouter as Router } from 'react-router-dom';
import cloneDeep from 'lodash/cloneDeep';
import { vi } from 'vitest';

import { InstanceItemProps } from '../../../src/constants/types';
import instanceStatsMockData from '../../data/userInstanceStats.json';
import InstanceItem from '../../../src/components/InstanceList/InstanceItem';

const baseUrl = 'http://localhost:3000/rest/v1';

const handlers = [
  rest.get(
    `${baseUrl}/config-management/user/instances/:instanceId/stats`,
    (_, res, ctx) => res(ctx.json(instanceStatsMockData))
  ),
];

const server = setupServer(...handlers);

const queryCache = new QueryCache();
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const fetchCsv = vi.fn();

const enqueueSnackbar = vi.fn();
vi.mock('notistack', () => ({
  useSnackbar: vi.fn().mockImplementation(() => ({ enqueueSnackbar })),
}));

const WrappedComponent = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    <Router>{children}</Router>
  </QueryClientProvider>
);

const mockInstanceItemProps: InstanceItemProps = {
  instance: {
    $id: '1',
    instanceName: 'instance A',
    appName: 'app A',
    configFile: 'base-config A',
    createdOn: '10 May 22',
    stats: {
      tenants: 1,
      sites: 1,
      siteTags: 1,
      devices: 1,
      lastUpdated: '10 may 2022',
    },
    schemaType: 'json-schema',
    configSchemaRenderType: 'Table',
  },
  fetchCsv,
};

describe('InstanceItem test', () => {
  beforeAll(() => {
    // Mock token is not empty to avoid unwanted token error.
    localStorage.setItem('token', 'Mock token');
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
    queryCache.clear();
  });

  afterAll(() => {
    server.close();
  });

  it('- should display correct text', () => {
    render(
      <WrappedComponent>
        <InstanceItem {...mockInstanceItemProps} />
      </WrappedComponent>
    );
    const {
      instance: { instanceName },
    } = mockInstanceItemProps;
    const instanceTitle = 'app A, base-config A Created on - 10 May 22';
    const typographyInstanceName = screen.queryByTestId('instance-name');
    expect(typographyInstanceName).toBeInTheDocument();
    expect(typographyInstanceName).toHaveTextContent(instanceName);
    const typographyTitle = screen.queryByTestId('instance-title');
    expect(typographyTitle).toBeInTheDocument();
    expect(typographyTitle).toHaveTextContent(instanceTitle);
  });

  it('- should show the row actions button and export csv inside if the instance is a bin table', () => {
    render(
      <WrappedComponent>
        <InstanceItem {...mockInstanceItemProps} />
      </WrappedComponent>
    );
    const rowActionsButton = screen.queryByTestId(
      'instance-row-actions-button'
    );
    expect(rowActionsButton).toBeInTheDocument();
  });

  it('- should not show the row actions button and export csv inside if the instance is not a bin table', async () => {
    const newMockInstanceItemProps = cloneDeep(mockInstanceItemProps);
    newMockInstanceItemProps.instance.configSchemaRenderType = 'Default';
    render(
      <WrappedComponent>
        <InstanceItem {...newMockInstanceItemProps} />
      </WrappedComponent>
    );
    const rowActionsButton = screen.queryByTestId(
      'instance-row-actions-button'
    );
    expect(rowActionsButton).toBeInTheDocument();
    fireEvent.click(rowActionsButton);
    await waitFor(async () => {
      const exportConfigInstanceButton = screen.queryByTestId(
        'export-config-instance-button'
      );
      expect(exportConfigInstanceButton).not.toBeInTheDocument();
    });
  });

  it('- should call fetchCsv with the correct instance Id', async () => {
    render(
      <WrappedComponent>
        <InstanceItem {...mockInstanceItemProps} />
      </WrappedComponent>
    );
    const rowActionsButton = screen.queryByTestId(
      'instance-row-actions-button'
    );
    expect(rowActionsButton).toBeInTheDocument();
    fireEvent.click(rowActionsButton);
    const exportConfigInstanceButton = screen.queryByTestId(
      'export-config-instance-button'
    );
    await waitFor(async () => {
      expect(exportConfigInstanceButton).toBeInTheDocument();
    });
    fireEvent.click(exportConfigInstanceButton);
    await waitFor(() => {
      expect(fetchCsv).toHaveBeenCalledWith(
        mockInstanceItemProps.instance.$id,
        mockInstanceItemProps.instance.instanceName
      );
    });
  });
});
